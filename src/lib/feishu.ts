import { Client } from '@larksuiteoapi/node-sdk';

// 飞书客户端配置
const client = new Client({
  appId: process.env.FEISHU_APP_ID!,
  appSecret: process.env.FEISHU_APP_SECRET!,
  disableTokenCache: false, // 启用token缓存，SDK自动管理token
});

// 飞书表格配置
const FEISHU_CONFIG = {
  appToken: process.env.FEISHU_APP_TOKEN!,
  tableId: process.env.FEISHU_TABLE_ID!,
};

// 账号状态统计接口
export interface AccountStats {
  total: number;      // 总账号数（注册=true AND 促销=true）
  sold: number;       // 已售账号数（已售=true）
  available: number;  // 库存数量（注册=true AND 促销=true AND 已售=false）
  soldRate: string;   // 销售率（已售/总数）
}

// 账号记录接口
export interface AccountRecord {
  recordId: string;
  account: string;
  registered: boolean;
  promotion: boolean;
  sold: boolean;
  registrationDate?: number;
  email?: string;
}

/**
 * 快速检查库存状态（仅返回可用数量）
 * @returns Promise<number> 可用账号数量
 */
export async function checkInventoryStatus(): Promise<number> {
  try {
    const response = await client.bitable.v1.appTableRecord.search({
      path: {
        app_token: FEISHU_CONFIG.appToken,
        table_id: FEISHU_CONFIG.tableId,
      },
      params: {
        page_size: 500, // 获取足够的数据进行统计
      },
      data: {
        filter: {
          conjunction: 'and',
          conditions: [
            {
              field_name: '注册',
              operator: 'is',
              value: ['true'], // 必须已注册
            },
            {
              field_name: '促销',
              operator: 'is',
              value: ['true'], // 必须是促销账号
            },
            {
              field_name: '已售',
              operator: 'is',
              value: ['false'], // 必须未售出
            },
          ],
        },
        automatic_fields: false,
      },
    });

    if (response.code === 0 && response.data?.items) {
      // 过滤并统计可用账号数量
      const availableCount = response.data.items.filter(record =>
        record.fields?.注册 === true &&
        record.fields?.促销 === true &&
        record.fields?.已售 !== true
      ).length;

      return availableCount;
    } else {
      throw new Error(`飞书API调用失败: ${response.msg}`);
    }
  } catch (error) {
    console.error('检查库存状态失败:', error);
    throw new Error('检查库存状态失败');
  }
}

/**
 * 获取账号统计信息
 * @returns Promise<AccountStats> 账号统计数据
 */
export async function getAccountStats(): Promise<AccountStats> {
  try {
    let allRecords: any[] = [];
    let pageToken: string | undefined;
    let hasMore = true;

    // 分页获取所有记录
    while (hasMore) {
      const response = await client.bitable.v1.appTableRecord.search({
        path: {
          app_token: FEISHU_CONFIG.appToken,
          table_id: FEISHU_CONFIG.tableId,
        },
        params: {
          page_size: 500, // 每页最大500条
          page_token: pageToken,
        },
        data: {
          automatic_fields: false, // 不需要自动字段
        },
      });

      if (response.code === 0 && response.data) {
        allRecords = allRecords.concat(response.data.items || []);
        hasMore = response.data.has_more || false;
        pageToken = response.data.page_token;
      } else {
        throw new Error(`飞书API调用失败: ${response.msg}`);
      }
    }

    // 统计数据 - 修正逻辑
    // 总账号数：注册=true AND 促销=true
    const total = allRecords.filter(record =>
      record.fields?.注册 === true && record.fields?.促销 === true
    ).length;

    // 已售数量：已售=true
    const sold = allRecords.filter(record => record.fields?.已售 === true).length;

    // 库存数量：注册=true AND 促销=true AND 已售=false
    const available = allRecords.filter(record =>
      record.fields?.注册 === true &&
      record.fields?.促销 === true &&
      record.fields?.已售 !== true
    ).length;

    // 销售率：已售/总数
    const soldRate = total > 0 ? ((sold / total) * 100).toFixed(1) : '0.0';

    return {
      total,
      sold,
      available,
      soldRate: `${soldRate}%`,
    };
  } catch (error) {
    console.error('获取账号统计失败:', error);
    throw new Error('获取账号统计失败');
  }
}

/**
 * 获取可用账号列表（库存账号）
 * @param limit 限制返回数量，默认10
 * @returns Promise<AccountRecord[]> 可用账号列表
 */
export async function getAvailableAccounts(limit: number = 10): Promise<AccountRecord[]> {
  try {
    const response = await client.bitable.v1.appTableRecord.search({
      path: {
        app_token: FEISHU_CONFIG.appToken,
        table_id: FEISHU_CONFIG.tableId,
      },
      params: {
        page_size: Math.min(limit * 3, 500), // 获取更多数据以确保有足够的可用账号
      },
      data: {
        filter: {
          conjunction: 'and',
          conditions: [
            {
              field_name: '注册',
              operator: 'is',
              value: ['true'], // 必须已注册
            },
            {
              field_name: '促销',
              operator: 'is',
              value: ['true'], // 必须是促销账号
            },
            {
              field_name: '已售',
              operator: 'is',
              value: ['false'], // 必须未售出
            },
          ],
        },
        automatic_fields: false,
      },
    });

    if (response.code === 0 && response.data?.items) {
      return response.data.items
        .filter(record =>
          // 三重过滤确保符合库存条件
          record.fields?.注册 === true &&
          record.fields?.促销 === true &&
          record.fields?.已售 !== true
        )
        .slice(0, limit) // 限制返回数量
        .map(record => ({
          recordId: record.record_id,
          account: record.fields?.账号?.[0]?.text || '',
          registered: record.fields?.注册 === true,
          promotion: record.fields?.促销 === true,
          sold: record.fields?.已售 === true,
          registrationDate: record.fields?.注册日期,
          email: record.fields?.email?.[0]?.text || record.fields?.email?.[0]?.link?.replace('mailto:', ''),
        }));
    } else {
      throw new Error(`飞书API调用失败: ${response.msg}`);
    }
  } catch (error) {
    console.error('获取可用账号失败:', error);
    throw new Error('获取可用账号失败');
  }
}

/**
 * 标记账号为已售
 * @param recordId 记录ID
 * @param buyerEmail 购买者邮箱
 * @returns Promise<boolean> 是否成功
 */
export async function markAccountAsSold(recordId: string, buyerEmail: string): Promise<boolean> {
  try {
    const response = await client.bitable.v1.appTableRecord.update({
      path: {
        app_token: FEISHU_CONFIG.appToken,
        table_id: FEISHU_CONFIG.tableId,
        record_id: recordId,
      },
      data: {
        fields: {
          已售: true,
          email: buyerEmail,
        },
      },
    });

    if (response.code === 0) {
      console.log(`账号已标记为已售: ${recordId}, 购买者: ${buyerEmail}`);
      return true;
    } else {
      console.error(`标记账号失败: ${response.msg}`);
      return false;
    }
  } catch (error) {
    console.error('标记账号为已售失败:', error);
    return false;
  }
}

/**
 * 分配账号给用户（原子操作）
 * @param buyerEmail 购买者邮箱
 * @returns Promise<{success: boolean, account?: AccountRecord, error?: string}> 分配结果
 */
export async function allocateAccountToUser(buyerEmail: string): Promise<{
  success: boolean;
  account?: AccountRecord;
  error?: string;
}> {
  try {
    // 1. 获取一个可用账号
    const availableAccounts = await getAvailableAccounts(1);

    if (availableAccounts.length === 0) {
      return {
        success: false,
        error: '暂无可用账号，请稍后再试'
      };
    }

    const account = availableAccounts[0];

    // 2. 标记账号为已售
    const markResult = await markAccountAsSold(account.recordId, buyerEmail);

    if (!markResult) {
      return {
        success: false,
        error: '账号分配失败，请联系客服'
      };
    }

    // 3. 返回分配的账号信息
    return {
      success: true,
      account: {
        ...account,
        sold: true,
        email: buyerEmail
      }
    };
  } catch (error) {
    console.error('分配账号失败:', error);
    return {
      success: false,
      error: '账号分配失败，请联系客服'
    };
  }
}

/**
 * 测试飞书连接
 * @returns Promise<boolean> 连接是否成功
 */
export async function testFeishuConnection(): Promise<boolean> {
  try {
    const response = await client.bitable.v1.appTable.list({
      path: {
        app_token: FEISHU_CONFIG.appToken,
      },
      params: {
        page_size: 1,
      },
    });

    return response.code === 0;
  } catch (error) {
    console.error('飞书连接测试失败:', error);
    return false;
  }
}
