'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function TestInventoryPage() {
  const [inventoryStatus, setInventoryStatus] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const checkInventory = async () => {
    setLoading(true);
    setError('');
    try {
      const response = await fetch('/api/inventory/status');
      const data = await response.json();
      
      if (data.success) {
        setInventoryStatus(data.data);
      } else {
        setError(data.error || '获取库存状态失败');
      }
    } catch (err) {
      setError('网络请求失败');
      console.error('获取库存状态失败:', err);
    } finally {
      setLoading(false);
    }
  };

  const testCreateOrder = async () => {
    setLoading(true);
    setError('');
    try {
      const response = await fetch('/api/orders/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userEmail: '<EMAIL>' }),
      });

      const data = await response.json();
      
      if (data.success) {
        alert('订单创建成功！订单ID: ' + data.data.orderId);
      } else {
        setError(data.error || '创建订单失败');
      }
    } catch (err) {
      setError('网络请求失败');
      console.error('创建订单失败:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <h1 className="text-3xl font-bold text-gray-900">库存和订单测试页面</h1>
        
        <div className="grid md:grid-cols-2 gap-6">
          {/* 库存状态测试 */}
          <Card>
            <CardHeader>
              <CardTitle>库存状态检查</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button onClick={checkInventory} disabled={loading}>
                {loading ? '检查中...' : '检查库存状态'}
              </Button>
              
              {inventoryStatus && (
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h3 className="font-semibold mb-2">库存状态：</h3>
                  <p>可用数量: {inventoryStatus.available}</p>
                  <p>是否有库存: {inventoryStatus.inStock ? '是' : '否'}</p>
                </div>
              )}
              
              {error && (
                <div className="p-4 bg-red-50 border border-red-200 rounded-lg text-red-700">
                  错误: {error}
                </div>
              )}
            </CardContent>
          </Card>

          {/* 订单创建测试 */}
          <Card>
            <CardHeader>
              <CardTitle>订单创建测试</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button onClick={testCreateOrder} disabled={loading}>
                {loading ? '创建中...' : '测试创建订单'}
              </Button>
              
              <p className="text-sm text-gray-600">
                这将使用测试邮箱 <EMAIL> 创建订单
              </p>
              
              {error && (
                <div className="p-4 bg-red-50 border border-red-200 rounded-lg text-red-700">
                  错误: {error}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>测试说明</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm text-gray-600">
              <p>1. 点击"检查库存状态"按钮查看当前库存情况</p>
              <p>2. 点击"测试创建订单"按钮测试在当前库存状态下是否能创建订单</p>
              <p>3. 如果库存为0，创建订单应该会失败并显示相应错误信息</p>
              <p>4. 如果库存充足，创建订单应该会成功</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
