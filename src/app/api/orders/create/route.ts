import { NextRequest, NextResponse } from 'next/server';
import { OrderService, PaymentService } from '@/lib/database';
import { createWechatPayOrder } from '@/lib/wechat-pay';
import { generateOrderId, isValidEmail, amountToFen } from '@/lib/utils';
import { checkInventoryStatus } from '@/lib/feishu';
import { CreateOrderRequest, ApiResponse, CreateOrderResponse } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const body: CreateOrderRequest = await request.json();
    const { userEmail } = body;

    // 验证请求参数
    if (!userEmail) {
      return NextResponse.json({
        success: false,
        error: '邮箱地址不能为空'
      } as ApiResponse, { status: 400 });
    }

    if (!isValidEmail(userEmail)) {
      return NextResponse.json({
        success: false,
        error: '邮箱地址格式不正确'
      } as ApiResponse, { status: 400 });
    }

    // 检查库存状态
    try {
      const availableCount = await checkInventoryStatus();
      if (availableCount <= 0) {
        return NextResponse.json({
          success: false,
          error: '当前库存不足，暂无可用账号，请稍后再试'
        } as ApiResponse, { status: 400 });
      }
    } catch (error) {
      console.error('检查库存失败:', error);
      return NextResponse.json({
        success: false,
        error: '系统繁忙，请稍后再试'
      } as ApiResponse, { status: 500 });
    }

    // 获取价格配置
    const price = parseFloat(process.env.AUGMENT_ACCOUNT_PRICE || '99');
    const amountInFen = amountToFen(price);

    // 创建订单
    const order = await OrderService.createOrder(userEmail, amountInFen);

    // 获取客户端IP
    const clientIp = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     '127.0.0.1';

    // 创建微信支付订单
    const wechatPayResult = await createWechatPayOrder(
      order.id,
      amountInFen,
      'Augment 账号购买',
      clientIp
    );

    if (!wechatPayResult.success) {
      // 如果微信支付创建失败，更新订单状态为失败
      await OrderService.updateOrderStatus(order.id, 'FAILED');

      console.error('微信支付创建失败:', wechatPayResult.error);

      return NextResponse.json({
        success: false,
        error: wechatPayResult.error || '创建支付订单失败'
      } as ApiResponse, { status: 500 });
    }

    // 创建支付记录
    await PaymentService.createPayment(
      order.id,
      amountInFen,
      wechatPayResult.qrCodeUrl
    );

    // 返回成功响应
    const response: CreateOrderResponse = {
      orderId: order.id,
      qrCodeUrl: wechatPayResult.qrCodeUrl!,
      amount: price
    };

    return NextResponse.json({
      success: true,
      data: response
    } as ApiResponse<CreateOrderResponse>);

  } catch (error) {
    console.error('创建订单失败:', error);
    
    return NextResponse.json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse, { status: 500 });
  }
}
