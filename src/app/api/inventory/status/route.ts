import { NextResponse } from 'next/server';
import { checkInventoryStatus } from '@/lib/feishu';

export async function GET() {
  try {
    const availableCount = await checkInventoryStatus();
    
    return NextResponse.json({
      success: true,
      data: {
        available: availableCount,
        inStock: availableCount > 0
      }
    });
  } catch (error) {
    console.error('获取库存状态失败:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: '获取库存状态失败',
        message: error instanceof Error ? error.message : '未知错误',
      },
      { status: 500 }
    );
  }
}
