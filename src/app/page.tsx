'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { RefreshCw, ShoppingCart, Package, CheckCircle2, Sparkles, Zap, Mail, CreditCard, Rocket, Shield, Clock, Star, ArrowRight, MessageSquare, Users, Settings } from 'lucide-react';
import PurchaseForm from '@/components/PurchaseForm';

interface StatsData {
  total: number;
  sold: number;
  available: number;
  soldRate: string;
}

export default function HomePage() {
  const [stats, setStats] = useState<StatsData | null>(null);
  const [loading, setLoading] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'checking'>('checking');

  // 获取库存统计
  const fetchStats = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/feishu/stats');
      const data = await response.json();

      if (data.success) {
        setStats(data.data);
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 检查连接状态
  const checkConnection = async () => {
    try {
      const response = await fetch('/api/feishu/test');
      const data = await response.json();

      if (data.success && data.data.connected) {
        setConnectionStatus('connected');
      } else {
        setConnectionStatus('disconnected');
      }
    } catch (error) {
      console.error('检查连接状态失败:', error);
      setConnectionStatus('disconnected');
    }
  };

  // 刷新统计数据
  const refreshStats = () => {
    fetchStats();
    checkConnection();
  };

  // 页面加载时获取数据
  useEffect(() => {
    fetchStats();
    checkConnection();
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* 动态背景装饰 */}
      <div className="absolute inset-0">
        <div className="absolute top-0 -left-4 w-72 h-72 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
        <div className="absolute top-0 -right-4 w-72 h-72 bg-yellow-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
        <div className="absolute -bottom-8 left-20 w-72 h-72 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"></div>
      </div>

      {/* 网格背景 */}
      <div className="absolute inset-0 opacity-40">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='1.5'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          backgroundSize: '60px 60px'
        }}></div>
      </div>

      <div className="relative z-10 min-h-screen flex flex-col">
        {/* 精简的页面头部 */}
        <header className="bg-white/5 backdrop-blur-xl border-b border-white/10">
          <div className="container mx-auto px-6 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Sparkles className="w-7 h-7 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-white">Augment</h1>
                  <p className="text-blue-200 text-sm">专业账号服务平台</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <div className={`w-3 h-3 rounded-full ${
                  connectionStatus === 'connected' ? 'bg-emerald-400 shadow-lg shadow-emerald-400/50' :
                  connectionStatus === 'disconnected' ? 'bg-red-400 shadow-lg shadow-red-400/50' : 'bg-yellow-400 shadow-lg shadow-yellow-400/50'
                } animate-pulse`}></div>
                <span className="text-white/90 text-sm font-medium">
                  {connectionStatus === 'connected' ? '服务正常' :
                   connectionStatus === 'disconnected' ? '连接异常' : '检查中...'}
                </span>
              </div>
            </div>
          </div>
        </header>

        {/* 主要内容区域 */}
        <main className="flex-1 flex items-center justify-center px-6 py-12">
          <div className="w-full max-w-4xl mx-auto space-y-12">

            {/* 品牌标题和产品介绍 */}
            <div className="text-center space-y-8">
              <div className="space-y-6">
                <h2 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-200 bg-clip-text text-transparent leading-tight">
                  AugmentCode 账号租赁
                </h2>
                <p className="text-2xl text-white/90 max-w-3xl mx-auto leading-relaxed">
                  专业AI编程助手 · <span className="text-emerald-400 font-bold">600次消息/月</span> · <span className="text-blue-400 font-bold">独享账号</span> · <span className="text-purple-400 font-bold">即买即用</span>
                </p>
                <p className="text-lg text-white/70 max-w-2xl mx-auto">
                  成品号免配置，无需安装插件，支付成功立即使用
                </p>
              </div>

              {/* 核心特色 */}
              <div className="grid md:grid-cols-4 gap-6 max-w-5xl mx-auto">
                <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20 hover:scale-105 transition-all duration-300 animate-fade-in">
                  <div className="w-12 h-12 bg-gradient-to-r from-emerald-500 to-green-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                    <MessageSquare className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-lg font-bold text-white mb-2">600次/月</h3>
                  <p className="text-white/70 text-sm">充足的消息额度<br/>满足日常开发需求</p>
                </div>

                <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20 hover:scale-105 transition-all duration-300 animate-fade-in" style={{animationDelay: '0.1s'}}>
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                    <Users className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-lg font-bold text-white mb-2">独享账号</h3>
                  <p className="text-white/70 text-sm">专属个人使用<br/>不与他人共享</p>
                </div>

                <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20 hover:scale-105 transition-all duration-300 animate-fade-in" style={{animationDelay: '0.2s'}}>
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                    <Settings className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-lg font-bold text-white mb-2">成品号</h3>
                  <p className="text-white/70 text-sm">无需配置安装<br/>开箱即用</p>
                </div>

                <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20 hover:scale-105 transition-all duration-300 animate-fade-in" style={{animationDelay: '0.3s'}}>
                  <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                    <Rocket className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-lg font-bold text-white mb-2">即时交付</h3>
                  <p className="text-white/70 text-sm">支付成功后<br/>立即发送至邮箱</p>
                </div>
              </div>
            </div>

            {/* 操作流程指引 */}
            <div className="text-center space-y-8">
              <h3 className="text-3xl font-bold text-white mb-8">
                三步即可开始使用
              </h3>
              <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
                <div className="relative">
                  <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-8 border border-white/20 hover:scale-105 transition-all duration-300 animate-fade-in">
                    <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-6 text-2xl font-bold text-white shadow-2xl">
                      1
                    </div>
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center mx-auto mb-4">
                      <Mail className="w-6 h-6 text-white" />
                    </div>
                    <h4 className="text-xl font-bold text-white mb-3">输入邮箱</h4>
                    <p className="text-white/70">填写您的邮箱地址<br/>用于接收账号信息</p>
                  </div>
                  {/* 连接线 */}
                  <div className="hidden md:block absolute top-1/2 -right-4 transform -translate-y-1/2">
                    <ArrowRight className="w-8 h-8 text-white/30" />
                  </div>
                </div>

                <div className="relative">
                  <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-8 border border-white/20 hover:scale-105 transition-all duration-300 animate-fade-in" style={{animationDelay: '0.2s'}}>
                    <div className="w-16 h-16 bg-gradient-to-r from-emerald-500 to-green-500 rounded-full flex items-center justify-center mx-auto mb-6 text-2xl font-bold text-white shadow-2xl">
                      2
                    </div>
                    <div className="w-12 h-12 bg-gradient-to-r from-emerald-500 to-green-500 rounded-xl flex items-center justify-center mx-auto mb-4">
                      <CreditCard className="w-6 h-6 text-white" />
                    </div>
                    <h4 className="text-xl font-bold text-white mb-3">微信支付</h4>
                    <p className="text-white/70">扫码支付，安全快捷<br/>支持微信支付</p>
                  </div>
                  <div className="hidden md:block absolute top-1/2 -right-4 transform -translate-y-1/2">
                    <ArrowRight className="w-8 h-8 text-white/30" />
                  </div>
                </div>

                <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-8 border border-white/20 hover:scale-105 transition-all duration-300 animate-fade-in" style={{animationDelay: '0.4s'}}>
                  <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-6 text-2xl font-bold text-white shadow-2xl">
                    3
                  </div>
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center mx-auto mb-4">
                    <Rocket className="w-6 h-6 text-white" />
                  </div>
                  <h4 className="text-xl font-bold text-white mb-3">立即使用</h4>
                  <p className="text-white/70">账号信息发送至邮箱<br/>即刻开始编程之旅</p>
                </div>
              </div>
            </div>

            {/* 简化的库存信息 */}
            <div className="text-center space-y-6">
              <h3 className="text-2xl font-bold text-white">
                实时库存状态
              </h3>
              <div className="flex items-center justify-center mb-8">
                <Button
                  onClick={refreshStats}
                  disabled={loading}
                  variant="outline"
                  size="sm"
                  className="bg-white/10 backdrop-blur-md border-white/20 text-white hover:bg-white/20 transition-all duration-300"
                >
                  <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                  刷新库存
                </Button>
              </div>
            </div>

            <div className="grid md:grid-cols-2 gap-6 max-w-2xl mx-auto">
              {/* 已售出数量 */}
              <Card className="bg-white/10 backdrop-blur-xl border-white/20 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:scale-105 group animate-fade-in">
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-emerald-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:shadow-emerald-500/25 transition-all duration-300 animate-float">
                    <CheckCircle2 className="w-8 h-8 text-white" />
                  </div>
                  <div className="text-4xl font-bold text-emerald-400 mb-2 transition-all duration-300 group-hover:scale-110">
                    {stats?.sold || 0}
                  </div>
                  <p className="text-white/70 font-medium">已售出</p>
                </CardContent>
              </Card>

              {/* 当前库存 */}
              <Card className="bg-white/10 backdrop-blur-xl border-white/20 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:scale-105 group animate-fade-in" style={{animationDelay: '0.2s'}}>
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300 animate-float" style={{animationDelay: '1s'}}>
                    <Package className="w-8 h-8 text-white" />
                  </div>
                  <div className="text-4xl font-bold text-blue-400 mb-2 transition-all duration-300 group-hover:scale-110">
                    {stats?.available || 0}
                  </div>
                  <p className="text-white/70 font-medium">
                    {(stats?.available || 0) > 0 ? '库存充足' : '库存不足'}
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* 购买表单区域 */}
            <div className="max-w-2xl mx-auto">
              <div className="text-center mb-8">
                <h3 className="text-3xl font-bold text-white mb-4">
                  开始购买 AugmentCode 账号
                </h3>
                <p className="text-lg text-white/80">
                  填写邮箱，完成支付，立即获得专属账号
                </p>
              </div>
              <Card className="bg-white/10 backdrop-blur-xl border-white/20 shadow-2xl hover:shadow-3xl transition-all duration-500 animate-zoom-in">
                <CardHeader className="text-center pb-6">
                  <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg animate-pulse-glow">
                    <ShoppingCart className="w-8 h-8 text-white" />
                  </div>
                  <CardTitle className="text-2xl font-bold text-white mb-2">
                    立即购买
                  </CardTitle>
                  <div className="bg-gradient-to-r from-emerald-500/20 to-blue-500/20 rounded-xl p-4 border border-emerald-500/30">
                    <p className="text-emerald-300 font-semibold text-lg">
                      ¥一个月 · 600次消息 · 独享账号
                    </p>
                  </div>
                </CardHeader>
                <CardContent>
                  <PurchaseForm />
                </CardContent>
              </Card>
            </div>

            {/* 产品优势说明 */}
            <div className="text-center space-y-8">
              <h3 className="text-2xl font-bold text-white">
                为什么选择我们的 AugmentCode 账号？
              </h3>
              <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                <div className="bg-white/5 backdrop-blur-xl rounded-2xl p-6 border border-white/10 text-left">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-emerald-500 to-green-600 rounded-xl flex items-center justify-center flex-shrink-0">
                      <Star className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h4 className="text-lg font-bold text-white mb-2">专业AI编程助手</h4>
                      <p className="text-white/70 text-sm leading-relaxed">
                        AugmentCode 是业界领先的AI编程助手，支持多种编程语言，提供智能代码补全、错误检测、代码优化等功能，大幅提升开发效率。
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-white/5 backdrop-blur-xl rounded-2xl p-6 border border-white/10 text-left">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0">
                      <Shield className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h4 className="text-lg font-bold text-white mb-2">独享账号保障</h4>
                      <p className="text-white/70 text-sm leading-relaxed">
                        每个账号仅供一人使用，确保您的使用体验不受影响。600次/月的消息额度，足够满足日常开发需求，无需担心额度不足。
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-white/5 backdrop-blur-xl rounded-2xl p-6 border border-white/10 text-left">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl flex items-center justify-center flex-shrink-0">
                      <Settings className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h4 className="text-lg font-bold text-white mb-2">开箱即用</h4>
                      <p className="text-white/70 text-sm leading-relaxed">
                        成品号免配置，无需安装任何插件或进行复杂设置。收到账号信息后，即可立即开始使用，节省您的宝贵时间。
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-white/5 backdrop-blur-xl rounded-2xl p-6 border border-white/10 text-left">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-600 rounded-xl flex items-center justify-center flex-shrink-0">
                      <Clock className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h4 className="text-lg font-bold text-white mb-2">即时交付服务</h4>
                      <p className="text-white/70 text-sm leading-relaxed">
                        支付成功后，账号信息将立即自动发送至您的邮箱。7x24小时自动化服务，随时购买，随时使用。
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 底部说明 */}
            <div className="text-center space-y-6 pt-12 border-t border-white/10">
              <div className="bg-white/5 backdrop-blur-xl rounded-2xl p-8 border border-white/10 max-w-3xl mx-auto">
                <h4 className="text-xl font-bold text-white mb-4">
                  适用人群
                </h4>
                <div className="grid md:grid-cols-3 gap-6 text-sm">
                  <div className="text-center">
                    <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                      <Users className="w-5 h-5 text-white" />
                    </div>
                    <p className="text-white font-medium mb-1">开发者</p>
                    <p className="text-white/60">提升编程效率</p>
                  </div>
                  <div className="text-center">
                    <div className="w-10 h-10 bg-gradient-to-r from-emerald-500 to-green-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                      <Star className="w-5 h-5 text-white" />
                    </div>
                    <p className="text-white font-medium mb-1">学生</p>
                    <p className="text-white/60">学习编程助手</p>
                  </div>
                  <div className="text-center">
                    <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                      <Rocket className="w-5 h-5 text-white" />
                    </div>
                    <p className="text-white font-medium mb-1">企业团队</p>
                    <p className="text-white/60">团队协作开发</p>
                  </div>
                </div>
              </div>

              <p className="text-white/50 text-sm">
                © 2024 AugmentCode 账号租赁平台 · 专业AI编程助手服务
              </p>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}


